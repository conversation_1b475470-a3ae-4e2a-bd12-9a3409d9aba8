---
import { Image } from "astro:assets";
import { getDisplayName, getColoringPageUri } from "../../utils/collections";
import { getImageImporter } from "../../utils/images";

interface Props {
  id: string;
  title?: string;
  description: string;
  thumbnail?: string;
  categoryId: string;
  subcategoryId?: string;
  difficulty: string;
  slug: string;
  url?: string;
}

const {
  id,
  title,
  description,
  thumbnail,
  categoryId,
  subcategoryId,
  difficulty,
  slug,
  url,
} = Astro.props;

// Define difficulty badge colors
const difficultyColors: { [key: string]: string } = {
  easy: "badge-success",
  medium: "badge-warning",
  hard: "badge-secondary",
  expert: "badge-error",
};

const badgeClass = difficultyColors[difficulty] || "badge-primary";

// Generate default URL if not provided
const pageUrl =
  url ||
  `/${categoryId}/${subcategoryId || ""}/${slug}-coloring-page/`.replace(
    /\/\//g,
    "/"
  );

// Get the image importer for the thumbnail if provided
let thumbnailImage;
if (thumbnail) {
  try {
    thumbnailImage = getImageImporter(thumbnail);
  } catch (error) {
    console.warn(`Failed to load thumbnail image: ${thumbnail}`, error);
    thumbnailImage = null;
  }
}
---

<a
  href={pageUrl}
  class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow duration-300 h-full group overflow-hidden">
  <figure class="relative overflow-hidden aspect-square">
    {
      thumbnailImage ? (
        <Image
          src={thumbnailImage()}
          alt={title || getDisplayName(slug)}
          class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
          decoding="async"
          width={600}
          height={600}
          quality="mid"
          densities={["1.5x", "2x"]}
        />
      ) : (
        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
          <span class="text-gray-500">No image</span>
        </div>
      )
    }
    <div class="absolute top-2 right-2">
      <div class={`badge ${badgeClass} text-xs font-medium capitalize`}>
        {difficulty}
      </div>
    </div>
  </figure>
  <div class="card-body p-4">
    <h3
      class="card-title text-base font-bold group-hover:text-primary-600 transition-colors line-clamp-1">
      {title || getDisplayName(slug)}
    </h3>
    <p class="text-sm text-neutral-600 line-clamp-2">{description}</p>
    <div class="card-actions justify-end mt-2">
      <button class="btn btn-sm btn-outline btn-primary normal-case">
        Color now
      </button>
    </div>
  </div>
</a>
