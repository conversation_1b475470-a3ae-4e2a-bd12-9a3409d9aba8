[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "sdkit"
version = "********"
authors = [
  {name="cmdr2", email="<EMAIL>"},
]
description = "sdkit (stable diffusion kit) is an easy-to-use library for using Stable Diffusion in your AI Art projects. It is fast, feature-packed, and memory-efficient. It bundles Stable Diffusion along with commonly-used features (like SDXL, ControlNet, LoRA, Embeddings, GFPGAN, RealESRGAN, k-samplers, custom VAE etc). It also includes a model-downloader with a database of commonly used models, and advanced features like running in parallel on multiple GPUs, auto-scanning for malicious models etc."
readme = "README.md"
requires-python = ">=3.8.5"
classifiers = [
	"Programming Language :: Python :: 3",
	"License :: OSI Approved :: MIT License",
	"Operating System :: Microsoft :: Windows :: Windows 10",
	"Operating System :: Microsoft :: Windows :: Windows 11",
	"Operating System :: POSIX :: Linux",
	"Operating System :: MacOS",
]
keywords = ["stable diffusion", "ai", "art"]
dynamic = ["dependencies"]

[project.urls]
"Homepage" = "https://github.com/easydiffusion/sdkit"
"Bug Tracker" = "https://github.com/easydiffusion/sdkit/issues"

[tool.isort]
profile = "black"

[tool.black]
line-length = 120
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-vs"
testpaths = [
    "tests",
    "integration",
]
