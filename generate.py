import os
import csv
import shutil
import argparse
import re
import time
import sys

import sdkit
from sdkit import Context
from sdkit.models import load_model
from sdkit.generate import generate_images

# --- CONFIGURATION CONSTANTS ---
# File/Folder Paths
SRC_DIR = 'src'
SD_MODELS_DIR = 'sd'
CATEGORIES_CSV = 'categories.csv'
PAGES_CSV = 'pages.csv'

# Stable Diffusion Model Paths
BASE_MODEL_PATH = os.path.join(SD_MODELS_DIR, 'sd_xl_base_1.0.safetensors')
VAE_PATH = os.path.join(SD_MODELS_DIR, 'sdxl_vae.safetensors')
LORA_PATH = os.path.join(SD_MODELS_DIR, 'ColoringBookRedmond-ColoringBook-ColoringBookAF.safetensors')

# Default Stable Diffusion Settings
SD_DEFAULTS = {
    'steps': 25,
    'guidance': 7.5,
    'guidance_thumb': 9.0,  # Higher guidance for more vibrant thumbnails
    'lora_scale': 0.9,
    'sampler': 'dpmpp_2m',
}

# Image Orientation Resolutions
ORIENTATIONS = {
    'landscape': (1216, 832),
    'portrait': (832, 1216),
    'square': (1024, 1024),
}

# Prompt Templates
PAGE_POSITIVE_PROMPT = "coloring page of {subject}, {camera_angle}, {camera_shot}, coloring book style, line art, thin lines, clean outlines, clean vector style, minimalist, black and white, simple background, white background"
PAGE_NEGATIVE_PROMPT = "color, shading, gradient, shadows, shades, transparency, blurry, watermark, text, details, complex background, cropped, scanlines, tramlines, thick lines, hatch lines, crosshatching, dither, texture, noisy, noise, artifacts, ornaments, scribble, gray, dark, adult content, nude, naked"
CATEGORY_POSITIVE_PROMPT = "digital illustration of {prompt}, vibrant colors, friendly and welcoming, bright and cheerful, simple composition, professional thumbnail design, high quality digital art"
CATEGORY_NEGATIVE_PROMPT = "dark, gloomy, scary, realistic, photograph, blurry, low quality, watermark, text, signature, complex background, cluttered, crowded, too many details, adult content, nude, naked, cropped, noisy, noise, artifacts"

# Sampler Map
# from diffusers import EulerDiscreteScheduler, EulerAncestralDiscreteScheduler, DDIMScheduler, HeunDiscreteScheduler, DPMSolverMultistepScheduler, DPMSolverSDEScheduler
SAMPLER_MAP = {
    "euler": "euler",
    "euler_a": "euler_a",
    "ddim": "ddim",
    "heun": "heun",
    "dpmpp_2m": "dpmpp_2m",
    "dpmpp_2m_sde": "dpmpp_2m_sde",
}

# --- DATA LOADING FUNCTIONS ---
def load_data_from_csv(filepath):
    parent_categories = {}
    subcategories_to_process = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if not row['parent-category']:
                    category_slug = row['category']
                    parent_categories[category_slug] = {
                        "slug": category_slug, "name": row['display-name'], "description": row['description'],
                        "related": row['related-categories'].split(',') if row['related-categories'] else [],
                        "content": row['content'], "prompt": row.get('prompt', ''), "subcategories": []
                    }
                else:
                    subcategories_to_process.append(row)
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None
    for sub_row in subcategories_to_process:
        parent_slug = sub_row['parent-category']
        if parent_slug in parent_categories:
            parent_categories[parent_slug]['subcategories'].append({
                "slug": sub_row['category'], "name": sub_row['display-name'], "description": sub_row['description'],
                "related": sub_row['related-categories'].split(',') if sub_row['related-categories'] else [],
                "content": sub_row['content'], "prompt": sub_row.get('prompt', '')
            })
        else:
            print(f"Warning: Parent category '{parent_slug}' not found for subcategory '{sub_row['category']}'.")
    return list(parent_categories.values())

def load_pages_data_from_csv(filepath):
    pages = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                pages.append(row)
        return pages
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None

# --- FILE & DIRECTORY SYNC FUNCTIONS ---
def sync_category_markdown(base_path, categories):
    print(f"\n--- Syncing Category Markdown Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category["slug"]), exist_ok=True)
        parent_file_path = os.path.join(base_path, f'{category["slug"]}.md')
        related_string = "[" + ", ".join([f'"{s}"' for s in category.get("related", []) if s]) + "]"
        with open(parent_file_path, 'w', encoding='utf-8') as f:
            f.write(f'---\nname: "{category["name"]}"\ndescription: "{category["description"]}"\nrelatedCategories: {related_string}\n---\n{category["content"]}\n')
        for subcategory in category["subcategories"]:
            sub_file_path = os.path.join(base_path, category["slug"], f'{subcategory["slug"]}.md')
            related_string = "[" + ", ".join([f'"{s}"' for s in subcategory.get("related", []) if s]) + "]"
            with open(sub_file_path, 'w', encoding='utf-8') as f:
                f.write(f'---\nname: "{subcategory["name"]}"\ndescription: "{subcategory["description"]}"\nrelatedCategories: {related_string}\n---\n{subcategory["content"]}\n')

def sync_page_markdown(base_path, pages_data):
    print(f"\n--- Syncing Coloring Page Markdown Files in '{base_path}' ---")
    if not pages_data: return
    for page in pages_data:
        page_dir = os.path.join(base_path, page['parent-category'], page['category'])
        os.makedirs(page_dir, exist_ok=True)
        file_path = os.path.join(page_dir, f"{page['slug']}.md")
        title = page['title'].replace('"', '\\"')
        description = page['description'].replace('"', '\\"')
        prompt = page['prompt'].replace('"', '\\"')
        yaml_content = f"""---
title: "{title}"
description: "{description}"
difficulty: "{page['difficulty']}"
orientation: "{page['orientation']}"
---
{page['content']}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

def sync_directory_structure(base_path, categories):
    print(f"\n--- Syncing Directory Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category['slug']), exist_ok=True)
        for subcategory in category['subcategories']:
            os.makedirs(os.path.join(base_path, category['slug'], subcategory['slug']), exist_ok=True)

def get_expected_paths(categories, pages, md_base, coloring_pages_base):
    expected_md, expected_coloring = set(), set()
    for cat in categories:
        expected_md.add(os.path.normpath(os.path.join(md_base, f"{cat['slug']}.md")))
        for base_path, path_set in [(md_base, expected_md), (coloring_pages_base, expected_coloring)]:
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'])))
        for sub in cat['subcategories']:
            expected_md.add(os.path.normpath(os.path.join(md_base, cat['slug'], f"{sub['slug']}.md")))
            path_set = expected_coloring
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'], sub['slug'])))
    if pages:
        for page in pages:
            page_path = os.path.join(coloring_pages_base, page['parent-category'], page['category'], f"{page['slug']}.md")
            expected_coloring.add(os.path.normpath(page_path))
    return expected_md, expected_coloring

def get_expected_image_paths(category_data, pages_data, image_base_path):
    """Generates a set of all valid image file paths, including variants."""
    expected_paths = set()
    # Add category thumbnail paths
    for cat in category_data:
        expected_paths.add(os.path.normpath(os.path.join(image_base_path, f"{cat['slug']}.png")))
        for sub in cat['subcategories']:
            expected_paths.add(os.path.normpath(os.path.join(image_base_path, cat['slug'], f"{sub['slug']}.png")))
    # Add page image paths
    if pages_data:
        for page in pages_data:
            # This logic assumes a max of 9 variants, which is reasonable.
            for i in range(10):
                variant_suffix = "" if i == 0 else f"-{i+1}"
                path = os.path.join(image_base_path, page['parent-category'], page['category'], f"{page['slug']}{variant_suffix}.png")
                expected_paths.add(os.path.normpath(path))
    return expected_paths

def cleanup_orphans(base_path, expected_paths):
    print(f"\n--- Cleaning orphaned files/folders in '{base_path}' ---")
    if not os.path.exists(base_path): return
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            file_path = os.path.normpath(os.path.join(root, name))
            if file_path not in expected_paths:
                print(f"Removing orphaned file: {file_path}")
                os.remove(file_path)
        for name in dirs:
            dir_path = os.path.normpath(os.path.join(root, name))
            if dir_path not in expected_paths:
                print(f"Removing orphaned directory: {dir_path}")
                shutil.rmtree(dir_path)

def cleanup_orphan_images(base_path, expected_image_paths):
    print(f"\n--- Cleaning orphaned images in '{base_path}' ---")
    if not os.path.exists(base_path): return
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            if name.endswith('.png'):
                file_path = os.path.normpath(os.path.join(root, name))
                if file_path not in expected_image_paths:
                    print(f"Removing orphaned image: {file_path}")
                    os.remove(file_path)
        if os.path.exists(root) and not os.listdir(root):
            print(f"Removing empty image directory: {root}")
            os.rmdir(root)

# --- IMAGE GENERATION FUNCTIONS ---
def generate_category_thumbnails(context, category_data, image_base_path, variants, steps, guidance_thumb):
    print("\n--- Generating Category Thumbnails ---")
    generated_count = 0
    for cat in category_data:
        # Process parent category
        if cat['prompt']:
            path = os.path.join(image_base_path, f"{cat['slug']}.png")
            if not os.path.exists(path):
                generated_count += 1
                prompt = CATEGORY_POSITIVE_PROMPT.format(prompt=cat['prompt'])
                print(f"  -> Generating {variants} variant(s) for category '{cat['slug']}'...")
                images = generate_images(
                    context,
                    prompt=prompt,
                    negative_prompt=CATEGORY_NEGATIVE_PROMPT,
                    num_inference_steps=steps,
                    guidance_scale=guidance_thumb,
                    width=1024,
                    height=1024,
                    num_outputs=variants,
                    # add_watermarker=False # Removed as this option is not available in sdkit
                )
                for idx, img in enumerate(images):
                    save_path = path if idx == 0 else os.path.join(image_base_path, f"{cat['slug']}-{idx + 1}.png")
                    img.save(save_path)
                    print(f"  -> Saved: {save_path}")
        # Process subcategories
        for sub in cat['subcategories']:
            if sub['prompt']:
                path = os.path.join(image_base_path, cat['slug'], f"{sub['slug']}.png")
                if not os.path.exists(path):
                    generated_count += 1
                    prompt = CATEGORY_POSITIVE_PROMPT.format(prompt=sub['prompt'])
                    print(f"  -> Generating {variants} variant(s) for subcategory '{sub['slug']}'...")
                    images = generate_images(
                        context,
                        prompt=prompt,
                        negative_prompt=CATEGORY_NEGATIVE_PROMPT,
                        num_inference_steps=steps,
                        guidance_scale=guidance_thumb,
                        width=1024,
                        height=1024,
                        num_outputs=variants,
                        # add_watermarker=False # Removed as this option is not available in sdkit
                    )
                    for idx, img in enumerate(images):
                        save_path = path if idx == 0 else os.path.join(image_base_path, cat['slug'], f"{sub['slug']}-{idx + 1}.png")
                        img.save(save_path)
                        print(f"  -> Saved: {save_path}")
    return generated_count

def generate_page_images(context, pages_data, image_base_path, variants, steps, guidance, lora_scale):
    print("\n--- Generating Coloring Page Images ---")
    generated_count = 0
    for i, page in enumerate(pages_data):
        print(f"\nProcessing page {i+1}/{len(pages_data)}: {page['slug']}")
        target_dir = os.path.join(image_base_path, page['parent-category'], page['category'])
        base_image_path = os.path.join(target_dir, f"{page['slug']}.png")
        if os.path.exists(base_image_path):
            print(f"  -> Skipping: Base image already exists.")
            continue
        generated_count += 1
        positive_prompt = PAGE_POSITIVE_PROMPT.format(subject=page['prompt'], camera_angle=page['camera-angle'], camera_shot=page['camera-shot'])
        width, height = ORIENTATIONS.get(page.get('orientation', 'portrait').lower(), ORIENTATIONS['portrait'])
        print(f"  -> Generating {variants} variant(s) for '{page['slug']}' at {width}x{height}...")
        images = generate_images(
            context,
            prompt=positive_prompt,
            negative_prompt=PAGE_NEGATIVE_PROMPT,
            num_inference_steps=steps,
            guidance_scale=guidance,
            lora_alpha=lora_scale,
            width=width,
            height=height,
            num_outputs=variants,
            # add_watermarker=False # Removed as this option is not available in sdkit
        )
        for idx, img in enumerate(images):
            save_path = base_image_path if idx == 0 else os.path.join(target_dir, f"{page['slug']}-{idx + 1}.png")
            img.save(save_path)
            print(f"  -> Saved: {save_path}")
    return generated_count

def run_image_generation_mode(category_data, pages_data, variants, steps, guidance, guidance_thumb, lora_scale, sampler_name):
    import torch
    # from diffusers import AutoencoderKL, StableDiffusionXLPipeline
    device = "cuda" if torch.cuda.is_available() else "cpu"
    if device == "cpu":
        print("CUDA is not available. Image generation requires a GPU.")
        return

    print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
    context = Context()
    context.device = device
    context.half_precision = True # Recommended for SDXL
    try:
        # torch_dtype = torch.float16 # sdkit handles precision internally based on context.half_precision
        load_model(context, "stable-diffusion", model_path=BASE_MODEL_PATH)
        load_model(context, "vae", model_path=VAE_PATH)

        # # Set scheduler # Samplers are set during image generation in sdkit
        # scheduler_class = SAMPLER_MAP.get(sampler_name)
        # if scheduler_class:
        #     if sampler_name in ["dpmpp_2m", "dpmpp_2m_sde"]:
        #         pipe.scheduler = scheduler_class.from_config(pipe.scheduler.config, use_karras_sigmas=True)
        #     else:
        #         pipe.scheduler = scheduler_class.from_config(pipe.scheduler.config)
        # else:
        #     print(f"Warning: Unknown sampler '{sampler_name}'. Using default EulerAncestralDiscreteScheduler.")
        #     pipe.scheduler = EulerAncestralDiscreteScheduler.from_config(pipe.scheduler.config)

        # pipe.to(device)
        # # Keep everything on GPU - no CPU offloading
        # #pipe.enable_model_cpu_offload()
        print("Pipeline initialized successfully.")
    except Exception as e:
        print(f"Error initializing Diffusion Pipeline: {e}")
        return

    image_base_path = os.path.join(SRC_DIR, 'assets', 'images')
    start_time = time.perf_counter()

    # 1. Generate category thumbnails (no LoRA) with higher guidance for vibrant colors
    cats_generated = generate_category_thumbnails(context, category_data, image_base_path, variants, steps, guidance_thumb)

    # 2. Load LoRA and generate page images
    print("\n--- Loading LoRA for page generation ---")
    load_model(context, "lora", model_path=LORA_PATH)
    pages_generated = generate_page_images(context, pages_data, image_base_path, variants, steps, guidance, lora_scale)

    end_time = time.perf_counter()
    duration = end_time - start_time
    total_items_generated = cats_generated + pages_generated

    if total_items_generated > 0:
        minutes, seconds = divmod(duration, 60)
        print("\n--- Image Generation Complete ---")
        print(f"Generated a total of {total_items_generated * variants} image(s).")
        print(f"Total time taken: {int(minutes)} minutes and {seconds:.2f} seconds.")
    else:
        print("\n--- No new images to generate. All items are up to date. ---")

def test_parameter_grid(context, page_data, image_base_path, seed=None):
    """Test a grid of parameters for a single page and save variants"""
    import torch

    # Parameter grid
    guidance_values = [
        # 7.0,
        8.0,
        # 9.0,
    ]
    lora_values = [
        # 0.8,
        1.0,
        # 1.2,
    ]
    steps_values = [
        # 20,
        25,
        # 30,
        # 50,
    ]
    schedulers = [
        ("euler", SAMPLER_MAP["euler"]),
        ("euler_a", SAMPLER_MAP["euler_a"]),
        # ("ddim", SAMPLER_MAP["ddim"]),
        ("heun", SAMPLER_MAP["heun"]),
        ("dpmpp_2m", SAMPLER_MAP["dpmpp_2m"]),
        ("dpmpp_2m_sde", SAMPLER_MAP["dpmpp_2m_sde"]),
    ]

    # Set seed for reproducibility
    if seed is not None:
        torch.manual_seed(seed)

    print(f"\n--- Testing Parameter Grid for '{page_data['slug']}' ---")
    print(f"Total combinations: {len(guidance_values) * len(lora_values) * len(steps_values) * len(schedulers)}")

    target_dir = os.path.join(image_base_path, page_data['parent-category'], page_data['category'])
    os.makedirs(target_dir, exist_ok=True)

    positive_prompt = PAGE_POSITIVE_PROMPT.format(
        subject=page_data['prompt'],
        camera_angle=page_data['camera-angle'],
        camera_shot=page_data['camera-shot']
    )
    width, height = ORIENTATIONS.get(page_data.get('orientation', 'portrait').lower(), ORIENTATIONS['portrait'])

    variant_count = 0

    for sched_name, sched_class in schedulers:
        # Set scheduler
        # sdkit handles schedulers directly in generate_images

        for guidance in guidance_values:
            for lora in lora_values:
                for steps in steps_values:
                    variant_count += 1

                    # Reset seed for each generation to ensure reproducibility
                    if seed is not None:
                        torch.manual_seed(seed)

                    filename = f"{page_data['slug']}_{sched_name}_s{steps}_l{lora}_g{guidance}.png"
                    save_path = os.path.join(target_dir, filename)

                    print(f"  -> Generating variant {variant_count}: guidance={guidance}, lora={lora}, steps={steps}, scheduler={sched_name}")

                    try:
                        image = generate_images(
                            context,
                            prompt=positive_prompt,
                            negative_prompt=PAGE_NEGATIVE_PROMPT,
                            num_inference_steps=steps,
                            guidance_scale=guidance,
                            lora_alpha=lora,
                            width=width,
                            height=height,
                            num_outputs=1,
                            sampler_name=sched_name,
                            # add_watermarker=False # Removed as this option is not available in sdkit
                        )[0]

                        image.save(save_path)
                        print(f"     Saved: {filename}")

                    except Exception as e:
                        print(f"     Error generating {filename}: {e}")

    print(f"\nGrid testing complete! Generated {variant_count} variants.")
    return variant_count

# --- MAIN EXECUTION BLOCK ---
if __name__ == "__main__":
    script_name = os.path.basename(sys.argv[0])
    help_epilog = f"""
Examples:

  1. Synchronize all content files and folders with the CSVs:
     python {script_name} content

  2. Generate missing images with default settings:
     python {script_name} images

  3. Generate 3 variants for each missing image:
     python {script_name} images --variants 3

  4. Generate missing images with custom settings:
     python {script_name} images --steps 30 --guidance 6.5 --guidance-thumb 8.0 --lora 1.0

  5. Generate missing images with 'dpmpp_2m' sampler:
     python {script_name} images --sampler dpmpp_2m

  6. Test image generation for a specific page with a grid of parameters, including different samplers:
     python {script_name} test --seed 64738 --page vibrant-parrot-in-the-jungle-coloring-page
"""
    parser = argparse.ArgumentParser(description="A tool to synchronize content and generate images for the coloring page site.", formatter_class=argparse.RawTextHelpFormatter, epilog=help_epilog)
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    content_parser = subparsers.add_parser("content", help="Synchronize folders and markdown files based on CSVs.")
    images_parser = subparsers.add_parser("images", help="Generate coloring page images for missing files.")
    images_parser.add_argument("--variants", type=int, default=1, help="Number of image variants to generate for each missing page.")
    images_parser.add_argument("--steps", type=int, default=SD_DEFAULTS['steps'], help=f"Number of inference steps (default: {SD_DEFAULTS['steps']}).")
    images_parser.add_argument("--guidance", type=float, default=SD_DEFAULTS['guidance'], help=f"Guidance scale for coloring pages (default: {SD_DEFAULTS['guidance']}).")
    images_parser.add_argument("--guidance-thumb", type=float, default=SD_DEFAULTS['guidance_thumb'], help=f"Guidance scale for category thumbnails (default: {SD_DEFAULTS['guidance_thumb']}).")
    images_parser.add_argument("--lora", type=float, default=SD_DEFAULTS['lora_scale'], dest='lora_scale', help=f"LoRA scale/weight (default: {SD_DEFAULTS['lora_scale']}).")
    images_parser.add_argument("--sampler", type=str, default=SD_DEFAULTS['sampler'], choices=list(SAMPLER_MAP.keys()), help=f"Sampler (scheduler) to use for image generation (default: {SD_DEFAULTS['sampler']}). Available: {', '.join(SAMPLER_MAP.keys())}")
    test_parser = subparsers.add_parser("test", help="Test image generation with a grid of parameters for a single page.")
    test_parser.add_argument("--seed", type=int, help="Seed for reproducible image generation.")
    test_parser.add_argument("--page", type=str, default="vibrant-parrot-in-the-jungle-coloring-page", help="Slug of the page to test (default: vibrant-parrot-in-the-jungle-coloring-page)")
    args = parser.parse_args()
    if not args.command:
        parser.print_help()
        sys.exit(0)

    category_data = load_data_from_csv(CATEGORIES_CSV)
    pages_data = load_pages_data_from_csv(PAGES_CSV)

    if args.command == "content":
        print("Running in Content Sync mode.")
        if category_data:
            md_path = os.path.join(SRC_DIR, 'content', 'categories')
            coloring_pages_path = os.path.join(SRC_DIR, 'content', 'coloring-pages')
            image_path = os.path.join(SRC_DIR, 'assets', 'images')
            expected_md, expected_coloring = get_expected_paths(category_data, pages_data, md_path, coloring_pages_path)
            expected_images = get_expected_image_paths(category_data, pages_data, image_path)
            cleanup_orphans(md_path, expected_md)
            cleanup_orphans(coloring_pages_path, expected_coloring)
            cleanup_orphan_images(image_path, expected_images)
            sync_category_markdown(md_path, category_data)
            sync_directory_structure(coloring_pages_path, category_data)
            sync_directory_structure(image_path, category_data)
            if pages_data:
                sync_page_markdown(coloring_pages_path, pages_data)
            print("\nSynchronization complete! Filesystem is now in sync with CSV data.")
        else:
            print("Could not load category data. Aborting script.")

    elif args.command == "images":
        print("Running in Image Generation mode.")
        if category_data and pages_data:
            run_image_generation_mode(
                category_data,
                pages_data,
                variants=args.variants,
                steps=args.steps,
                guidance=args.guidance,
                guidance_thumb=args.guidance_thumb,
                lora_scale=args.lora_scale,
                sampler_name=args.sampler
            )
        else:
            print("Could not load category and/or pages data. Aborting image generation.")

    elif args.command == "test":
        image_base_path = os.path.join(SRC_DIR, 'assets', 'images')
        # Find the page to test, defaulting to the specified slug
        page_slug = args.page if hasattr(args, 'page') and args.page else "vibrant-parrot-in-the-jungle-coloring-page"
        page_to_test = next((page for page in pages_data if page['slug'] == page_slug), None)
        if not page_to_test:
            print(f"Error: Could not find page with slug '{page_slug}' in pages CSV.")
            sys.exit(1)
        import torch
        # from diffusers import AutoencoderKL, StableDiffusionXLPipeline
        device = "cuda" if torch.cuda.is_available() else "cpu"
        if device == "cpu":
            print("CUDA is not available. Image generation requires a GPU.")
            sys.exit(1)

        print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
        context = Context()
        context.device = device
        context.half_precision = True # Recommended for SDXL
        try:
            # torch_dtype = torch.float16
            # vae = AutoencoderKL.from_single_file(VAE_PATH, torch_dtype=torch_dtype)
            # pipe = StableDiffusionXLPipeline.from_single_file(BASE_MODEL_PATH, vae=vae, torch_dtype=torch_dtype, use_safetensors=True)
            load_model(context, "stable-diffusion", model_path=BASE_MODEL_PATH)
            load_model(context, "vae", model_path=VAE_PATH)
            # pipe.to(device)
            print("Pipeline initialized successfully.")
        except Exception as e:
            print(f"Error initializing Diffusion Pipeline: {e}")
            sys.exit(1)

        # Load LoRA
        load_model(context, "lora", model_path=LORA_PATH)

        # Test the grid
        test_parameter_grid(context, page_to_test, image_base_path, seed=args.seed)