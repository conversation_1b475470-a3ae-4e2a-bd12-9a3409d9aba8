---
import { Image } from "astro:assets";
import { getDisplayName, getCategoryUri } from "../../utils/collections";
import { getImageImporter } from "../../utils/images";

interface Props {
  slug: string;
  name?: string;
  description: string;
  thumbnail: string;
}

const { slug, name, description, thumbnail } = Astro.props;

// Get the image importer for the thumbnail
let thumbnailImage;
try {
  thumbnailImage = getImageImporter(thumbnail);
} catch (error) {
  console.warn(`Failed to load thumbnail image: ${thumbnail}`, error);
  thumbnailImage = null;
}
---

<a
  href={getCategoryUri(slug)}
  class="card bg-base-100 shadow-md hover:shadow-lg transition-all duration-300 h-full group">
  <figure class="relative overflow-hidden aspect-[4/3]">
    {
      thumbnailImage ? (
        <Image
          src={thumbnailImage()}
          alt={`${name || getDisplayName(slug)} coloring pages category`}
          class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
          width={400}
          height={300}
        />
      ) : (
        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
          <span class="text-gray-500">No image</span>
        </div>
      )
    }
  </figure>
  <div class="card-body p-4">
    <h3
      class="card-title text-lg md:text-xl font-bold text-neutral group-hover:text-primary-600 transition-colors">
      {name || getDisplayName(slug)}
    </h3>
    <p class="text-sm text-neutral-600 line-clamp-2">{description}</p>
    <div class="card-actions justify-end mt-2">
      <span
        class="text-primary-600 text-sm font-semibold inline-flex items-center group-hover:translate-x-1 transition-transform">
        Browse pages
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
            clip-rule="evenodd"></path>
        </svg>
      </span>
    </div>
  </div>
</a>
